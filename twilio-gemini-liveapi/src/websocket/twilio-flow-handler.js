import { endSession } from './session-utils.js';
import { websocketLogger } from '../utils/logger.js';
import { globalHeartbeatManager } from './heartbeat-manager.js';

// Twilio flow handler for both inbound and outbound calls
export function handleTwilioFlow(connection, deps) {
    const {
        sessionManager,
        contextManager,
        activeConnections,
        healthMonitor,
        summaryManager,
        lifecycleManager,
        recoveryManager,
        transcriptionManager,
        flowType,
        getSessionConfig,
        isIncomingCall
    } = deps;

    const callSid = connection.query?.CallSid || `twilio-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
    websocketLogger.info(`Twilio ${flowType} call started`, { callSid, flowType, isIncomingCall });
    
    const ws = connection.socket || connection;
    
    let geminiSession = null;
    let isSessionActive = false;

    // Store event listeners for cleanup
    const eventListeners = new Map();

    const messageHandler = async (message) => {
        try {
            const data = JSON.parse(message);

            // T<PERSON><PERSON> uses 'event' field for message type
            const messageType = data.event;
            websocketLogger.debug(`Twilio ${flowType} message received`, { callSid, event: data.event, messageType });

            switch (messageType) {
                case 'start':
                    // Twilio sends 'start' event when stream begins
                    console.log(`🔍 [${callSid}] Twilio START event received:`, JSON.stringify(data, null, 2));
                    await handleTwilioStartSession(callSid, data, deps, ws, flowType, isIncomingCall, getSessionConfig, activeConnections, healthMonitor, lifecycleManager, sessionManager);
                    break;

                case 'media':
                    await handleTwilioMedia(callSid, data, geminiSession, isSessionActive, deps, activeConnections, lifecycleManager, recoveryManager);
                    break;

                case 'mark':
                    websocketLogger.debug('Mark received', { callSid });
                    break;

                case 'stop':
                    websocketLogger.info('Stop message received from Twilio', { callSid });
                    await handleTwilioEndSession(callSid, deps, activeConnections, lifecycleManager);
                    break;

                default:
                    websocketLogger.warn(`Unknown Twilio message event: ${messageType}`, { callSid });
                    console.log(`🔍 [${callSid}] Unknown message:`, JSON.stringify(data, null, 2));
            }
        } catch (error) {
            websocketLogger.error(`Error processing Twilio ${flowType} message`, error, { callSid });
        }
    };

    // Store handlers for cleanup
    eventListeners.set('message', messageHandler);
    ws.on('message', messageHandler);

    const closeHandler = async (code, reason) => {
        websocketLogger.info(`Twilio ${flowType} connection closed`, {
            callSid,
            code,
            reason: reason || 'No reason'
        });

        // Stop heartbeat monitoring
        globalHeartbeatManager.stopHeartbeat(callSid);
        
        const connectionData = activeConnections.get(callSid);
        if (connectionData) {
            if (lifecycleManager) {
                await lifecycleManager.requestSessionEnd(callSid, connectionData, 'twilio_connection_closed');
            } else {
                endSession(callSid, deps, 'twilio_connection_closed');
            }
        }

        // Clean up event listeners
        for (const [event, handler] of eventListeners) {
            ws.removeListener(event, handler);
        }
        eventListeners.clear();
    };

    const errorHandler = async (error) => {
        websocketLogger.error(`Twilio ${flowType} error`, error, { callSid });
        
        const connectionData = activeConnections.get(callSid);
        if (connectionData && recoveryManager && contextManager.canRecover(callSid)) {
            websocketLogger.info('Twilio WebSocket error detected, attempting session recovery', { callSid });
            contextManager.markSessionInterrupted(callSid, 'twilio_websocket_error');
            setTimeout(async () => {
                await recoveryManager.recoverSession(callSid, 'twilio_websocket_error', activeConnections);
            }, 1000);
        } else {
            // Clean up Deepgram transcription before ending session
            if (connectionData && connectionData.deepgramConnection) {
                deps.transcriptionManager.closeTranscription(callSid);
            }
            endSession(callSid, { ...deps, transcriptionManager: deps.transcriptionManager }, 'twilio_connection_error');
        }
    };

    // Register all event listeners
    eventListeners.set('close', closeHandler);
    eventListeners.set('error', errorHandler);
    ws.on('close', closeHandler);
    ws.on('error', errorHandler);
}

async function handleTwilioStartSession(callSid, data, deps, ws, flowType, isIncomingCall, getSessionConfig, activeConnections, healthMonitor, lifecycleManager, sessionManager) {
    websocketLogger.info(`Starting Twilio ${flowType} session`, { callSid });

    try {
        // Get session config from the stored configuration (set during webhook call)
        let sessionConfig = getSessionConfig();

        // Extract Twilio stream information
        const streamSid = data.start?.streamSid;
        const accountSid = data.start?.accountSid;
        const twilioCallSid = data.start?.callSid;

        console.log(`🔍 [${callSid}] Twilio stream info:`, {
            streamSid,
            accountSid,
            twilioCallSid,
            configFound: !!sessionConfig,
            hasInstructions: !!sessionConfig?.aiInstructions
        });

        // Ensure we have a valid session config
        if (!sessionConfig || !sessionConfig.aiInstructions) {
            console.warn(`⚠️ [${callSid}] No session config found, using fallback`);
            // Try to get current script as fallback
            try {
                const currentScript = isIncomingCall
                    ? deps.scriptManager.getCurrentIncomingScript()
                    : deps.scriptManager.getCurrentOutboundScript();

                if (currentScript) {
                    sessionConfig = deps.scriptManager.getScriptConfig(currentScript.id, isIncomingCall);
                    console.log(`✅ [${callSid}] Using fallback script: ${currentScript.id}`);
                }
            } catch (error) {
                console.error(`❌ [${callSid}] Error getting fallback script:`, error);
            }
        }

        // Store enhanced connection data
        const connectionData = {
            twilioWs: ws,
            callSid,
            isSessionActive: false,
            summaryRequested: false,
            summaryReceived: false,
            summaryText: '',
            conversationLog: [],
            fullTranscript: [],
            speechTranscript: [],
            isIncomingCall,
            sessionType: 'twilio_call',
            flowType,
            sessionStartTime: Date.now(),
            lastActivity: Date.now(),
            targetName: sessionConfig.targetName || 'Contact',
            targetPhoneNumber: sessionConfig.targetPhoneNumber || '+**********',
            originalAIInstructions: sessionConfig.aiInstructions,
            scriptId: sessionConfig.scriptId,
            isTwilioCall: true
        };
        
        activeConnections.set(callSid, connectionData);

        // Track connection health
        healthMonitor.trackConnection(callSid, 'connected', {
            flowType,
            isTwilioCall: true,
            scriptId: sessionConfig.scriptId
        });

        // Create Gemini session
        const geminiSession = await sessionManager.createGeminiSession(callSid, sessionConfig, connectionData);
        
        if (geminiSession) {
            connectionData.geminiSession = geminiSession;
            
            // Start WebSocket heartbeat monitoring for Twilio
            globalHeartbeatManager.startHeartbeat(
                callSid,
                ws,
                30000, // 30 second ping interval
                10000, // 10 second pong timeout
                (sessionId, ws) => {
                    websocketLogger.info('Twilio WebSocket heartbeat timeout', { callSid });
                    endSession(callSid, deps, 'heartbeat_timeout');
                }
            );

            // Send initial AI instructions
            if (sessionConfig.aiInstructions && geminiSession) {
                try {
                    websocketLogger.info(`Sending ${flowType} instructions to Gemini`, { callSid });

                    // For outbound calls, combine instructions with trigger in a single message
                    let messageText = sessionConfig.aiInstructions;
                    if (!isIncomingCall) {
                        websocketLogger.info(`Combining instructions with conversation trigger for outbound call`, { callSid });
                        messageText = `${sessionConfig.aiInstructions}\n\nThe call has been answered. Start the conversation immediately according to your instructions.`;
                    }

                    await geminiSession.sendClientContent({
                        turns: [{
                            role: 'user',
                            parts: [{
                                text: messageText
                            }]
                        }],
                        turnComplete: true
                    });

                    websocketLogger.info(`${flowType} instructions sent successfully`, { callSid });

                } catch (error) {
                    websocketLogger.error(`Failed to send initial AI instructions`, error, { callSid });
                }
            }

            ws.send(JSON.stringify({
                type: 'session-started',
                callSid,
                flowType,
                scriptId: sessionConfig.scriptId
            }));
            
            websocketLogger.info(`Twilio ${flowType} session started successfully`, { callSid });
        } else {
            // Gemini session failed to initialize
            websocketLogger.error(`Failed to create Gemini session for ${flowType} call`, { callSid });
            ws.send(JSON.stringify({
                type: 'session-error',
                error: 'Failed to initialize AI session. Please try again later.',
                critical: true
            }));
            
            // For Twilio calls, we should end the call gracefully
            if (deps.twilioHelper) {
                try {
                    await deps.twilioHelper.endCallWithMessage(callSid, 'We apologize, but we are unable to process your call at this time. Please try again later.');
                } catch (err) {
                    websocketLogger.error('Failed to end call with error message', err, { callSid });
                }
            }
            
            // Clean up and close connection
            endSession(callSid, deps, 'gemini_session_failed');
            ws.close();
            return;
        }

    } catch (error) {
        websocketLogger.error(`Error starting Twilio ${flowType} session`, error, { callSid });
        ws.send(JSON.stringify({
            type: 'session-error',
            error: `Session start failed: ${error.message}`
        }));
    }
}

async function handleTwilioMedia(callSid, data, geminiSession, isSessionActive, deps, activeConnections, lifecycleManager, recoveryManager) {
    if (data.media && data.media.payload) {
        try {
            // Update activity for session persistence
            lifecycleManager.updateActivity(callSid);

            // Convert Twilio μ-law audio to PCM
            const ulawAudio = Buffer.from(data.media.payload, 'base64');
            const pcmAudio = deps.audioProcessor.ulawToPcm(ulawAudio);
            const base64Pcm = pcmAudio.toString('base64');

            // Send to Gemini
            if (deps.sessionManager && geminiSession && isSessionActive) {
                await deps.sessionManager.sendAudioToGemini(callSid, geminiSession, base64Pcm);
            }

        } catch (error) {
            websocketLogger.error(`Error processing Twilio media`, error, { callSid });
            
            // Attempt recovery if needed
            const connectionData = activeConnections.get(callSid);
            if (connectionData && recoveryManager && recoveryManager.needsRecovery(callSid, activeConnections)) {
                await recoveryManager.recoverSession(callSid, 'media_processing_error', activeConnections);
            }
        }
    }
}

async function handleTwilioEndSession(callSid, deps, activeConnections, lifecycleManager) {
    websocketLogger.info('Ending Twilio session - stop received', { callSid });
    
    const connectionData = activeConnections.get(callSid);
    if (connectionData) {
        if (lifecycleManager) {
            await lifecycleManager.requestSessionEnd(callSid, connectionData, 'twilio_stop_received');
        } else {
            endSession(callSid, deps, 'twilio_stop_received');
        }
    }
}
