import { Modality } from '../gemini/client.js';
import { AudioProcessor } from '../audio/audio-processor.js';

// Bounded Map and Set for memory safety
class BoundedMap extends Map {
    constructor(maxSize = 1000) {
        super();
        this.maxSize = maxSize;
    }

    set(key, value) {
        if (this.size >= this.maxSize && !this.has(key)) {
            const firstKey = this.keys().next().value;
            this.delete(firstKey);
            console.log(`🧹 SessionManager BoundedMap: Removed oldest entry ${firstKey}`);
        }
        return super.set(key, value);
    }
}

class BoundedSet extends Set {
    constructor(maxSize = 1000) {
        super();
        this.maxSize = maxSize;
    }

    add(value) {
        if (this.size >= this.maxSize && !this.has(value)) {
            const firstValue = this.values().next().value;
            this.delete(firstValue);
            console.log(`🧹 SessionManager BoundedSet: Removed oldest entry ${firstValue}`);
        }
        return super.add(value);
    }
}

// Session Manager for Gemini connections with recovery
export class SessionManager {
    constructor(contextManager, geminiClient, activeConnections = null) {
        this.contextManager = contextManager;
        this.geminiClient = geminiClient;
        this.recoveryInProgress = new BoundedSet(200); // Limit recovery operations
        this.audioProcessor = new AudioProcessor();
        this.sessionMetrics = new BoundedMap(1000); // Limit session metrics
        this.activeConnections = activeConnections; // Reference to activeConnections for audio forwarding
    }

    // Create new Gemini session
    async createGeminiSession(callSid, config, connectionData) {
        try {
            console.log(`🤖 [${callSid}] Creating Gemini session with model: ${config.model}, voice: ${config.voice}`);
            console.log(`🔍 [${callSid}] ===== SESSION MANAGER MODEL DEBUG =====`);
            console.log(`🔍 [${callSid}] config.model = "${config.model}"`);
            console.log(`🔍 [${callSid}] config.voice = "${config.voice}"`);
            console.log(`🔍 [${callSid}] process.env.GEMINI_DEFAULT_MODEL = "${process.env.GEMINI_DEFAULT_MODEL}"`);
            console.log(`🔍 [${callSid}] ==========================================`);

            // Capture 'this' context for callbacks
            const self = this;

            // Initialize session metrics BEFORE creating session to avoid race conditions
            this.sessionMetrics.set(callSid, {
                startTime: Date.now(),
                messagesReceived: 0,
                messagesSent: 0,
                recoveryCount: 0,
                lastActivity: Date.now(),
                isInitializing: true
            });

            // Store reference immediately to prevent race conditions
            connectionData.geminiSession = null;
            connectionData.isSessionActive = false;

            // Create session and store reference immediately
            const geminiSession = await this.geminiClient.live.connect({
                model: config.model,
                callbacks: {
                    onopen: () => {
                        console.log(`✅ [${callSid}] Gemini session opened`);
                        connectionData.isSessionActive = true;

                        // Update metrics state
                        const metrics = this.sessionMetrics.get(callSid);
                        if (metrics) {
                            metrics.isInitializing = false;
                        }

                        // Save initial context with bounded arrays
                        this.contextManager.saveSessionContext(callSid, {
                            ...config,
                            ...connectionData,
                            conversationLog: [],
                            fullTranscript: [],
                            maxConversationLogSize: 500,
                            maxTranscriptSize: 1000
                        });

                        // AI instructions are sent after session creation, not here
                        // This prevents duplicate instructions being sent

                        console.log(`🔄 [${callSid}] Session initialized and ready for conversation`);
                    },

                    onerror: (error) => {
                        console.error(`❌ [${callSid}] Gemini session error:`, error);
                        this.contextManager.markSessionInterrupted(callSid, 'session_error');

                        // Update metrics
                        const metrics = this.sessionMetrics.get(callSid);
                        if (metrics) {
                            metrics.recoveryCount++;
                        }

                        // Mark session as inactive but don't end it - let recovery manager handle it
                        connectionData.isSessionActive = false;
                        connectionData.geminiSessionError = error.message;

                        console.log(`🔄 [${callSid}] Gemini session error detected, session marked for recovery`);
                        // Recovery will be handled by the recovery manager's health checks or explicit recovery calls
                    },

                    onclose: () => {
                        console.log(`🔌 [${callSid}] Gemini session closed`);
                        connectionData.isSessionActive = false;

                        // Session closed - cleanup handled by lifecycle manager

                        // Check if this is an unexpected close (connection still active)
                        const isUnexpectedClose = connectionData.twilioWs && connectionData.twilioWs.readyState === 1; // WebSocket.OPEN
                        const isLocalTestingActive = connectionData.localWs && connectionData.localWs.readyState === 1;

                        if (isUnexpectedClose || isLocalTestingActive) {
                            console.log(`⚠️ [${callSid}] Unexpected Gemini session close detected`);
                            this.contextManager.markSessionInterrupted(callSid, 'session_closed_unexpected');

                            // Session will be recovered by recovery manager's health checks or explicit recovery calls
                            console.log(`🔄 [${callSid}] Session marked for recovery due to unexpected close`);
                        } else {
                            console.log(`✅ [${callSid}] Gemini session closed normally (connection also closed)`);
                        }
                    },

                    onmessage: async (message) => {
                        try {
                            // Log Gemini API messages for debugging (excluding audio packets)
                            const hasAudio = message.serverContent?.modelTurn?.parts?.[0]?.inlineData;
                            if (!hasAudio) {
                                console.log(`📨 [${callSid}] Gemini API message (non-audio):`, JSON.stringify(message, null, 2));
                            } else {
                                console.log(`🎵 [${callSid}] Gemini audio packet received (${message.serverContent?.modelTurn?.parts?.[0]?.inlineData?.data?.length || 0} bytes)`);
                            }

                            // Enhanced message validation and handling
                            if (!message || !message.serverContent) {
                                console.warn(`⚠️ [${callSid}] Received invalid message structure`);
                                return;
                            }

                            // Update metrics with null check
                            const metrics = self?.sessionMetrics?.get(callSid);
                            if (metrics) {
                                metrics.messagesReceived = (metrics.messagesReceived || 0) + 1;
                                metrics.lastActivity = Date.now();
                            }

                            // Handle audio response from Gemini with validation
                            const audio = message.serverContent?.modelTurn?.parts?.[0]?.inlineData;

                            if (audio && audio.mimeType && audio.mimeType.includes('audio')) {
                                // Enhanced debugging for audio path
                                console.log(`🎵 [${callSid}] Gemini sent AUDIO response:`, {
                                    mimeType: audio.mimeType,
                                    dataLength: audio.data?.length || 0,
                                    hasData: !!audio.data
                                });
                                
                                // Validate audio data before processing
                                if (!audio.data || audio.data.length === 0) {
                                    console.warn(`⚠️ [${callSid}] Received empty audio data from Gemini`);
                                    return;
                                }

                                // Send audio to Twilio if connection is active
                                if (connectionData?.twilioWs && connectionData.twilioWs.readyState === 1) { // WebSocket.OPEN
                                    try {
                                        // Convert Gemini PCM audio to μ-law format for Twilio
                                        const convertedAudio = self?.audioProcessor?.convertPCMToUlaw(audio.data);
                                        if (convertedAudio) {
                                            const audioDelta = {
                                                event: 'media',
                                                streamSid: connectionData.streamSid,
                                                media: {
                                                    payload: convertedAudio
                                                }
                                            };
                                            connectionData.twilioWs.send(JSON.stringify(audioDelta));
                                            console.log(`🔊 [${callSid}] Sent converted audio to Twilio (${convertedAudio.length} chars)`);
                                        }
                                    } catch (audioError) {
                                        console.error(`❌ [${callSid}] Error sending audio to Twilio:`, audioError);
                                    }
                                }

                                // AUDIO FORWARDING FIX: Skip local WebSocket forwarding for local testing sessions
                                // The refactored local-testing-handler.js now handles audio forwarding for local sessions
                                if (connectionData.sessionType === 'local_test' || connectionData.sessionType === 'local' || connectionData.isTestMode) {
                                    console.log(`🔧 [${callSid}] Skipping session manager audio forwarding for local testing session (handled by refactored handler)`);
                                } else {
                                    // Only forward audio for non-local sessions (like Twilio calls that also need local WebSocket forwarding)
                                    console.log(`🔍 [${callSid}] Audio forwarding debug: localWs=${!!connectionData.localWs}, readyState=${connectionData.localWs?.readyState}, audioDataLength=${audio.data.length}`);

                                    // CRITICAL FIX: Try multiple ways to get the local WebSocket connection
                                    let localWs = connectionData.localWs;

                                    // If localWs is not available, try to get it from activeConnections
                                    if (!localWs || localWs.readyState !== 1) {
                                        console.log(`🔍 [${callSid}] Primary localWs not available, checking activeConnections...`);
                                        const storedConnection = this.activeConnections?.get(callSid);
                                        if (storedConnection?.localWs) {
                                            localWs = storedConnection.localWs;
                                            console.log(`🔧 [${callSid}] Found localWs in activeConnections: readyState=${localWs.readyState}`);
                                        }
                                    }

                                    if (localWs && localWs.readyState === 1) { // WebSocket.OPEN
                                        try {
                                            localWs.send(JSON.stringify({
                                                type: 'audio',
                                                audio: audio.data  // Raw PCM data for browser playback
                                            }));
                                            console.log(`🔊 [${callSid}] Sent raw PCM audio to local WebSocket (${audio.data.length} chars)`);
                                        } catch (audioError) {
                                            console.error(`❌ [${callSid}] Error sending audio to local WebSocket:`, audioError);
                                        }
                                    } else {
                                        console.warn(`⚠️ [${callSid}] Cannot forward audio to local WebSocket: localWs=${!!localWs}, readyState=${localWs?.readyState}`);
                                    }
                                }
                            }

                            // Handle text response for summary collection and conversation logging
                            const text = message.serverContent?.modelTurn?.parts?.[0]?.text;
                            if (text) {
                                console.log(`💬 [${callSid}] Gemini response: ${text.substring(0, 100)}...`);

                                // Update AI responsiveness tracking - CRITICAL FOR TURN MANAGEMENT
                                connectionData.lastAIResponse = Date.now();
                                connectionData.responseTimeouts = 0; // Reset timeout counter
                                connectionData.connectionQuality = 'good';

                                // Update metrics
                                if (metrics) {
                                    metrics.lastActivity = Date.now();
                                }

                                // Log conversation for recovery purposes - CRITICAL FOR CONTEXT PRESERVATION
                                if (connectionData.conversationLog) {
                                    // Implement bounded array to prevent memory leaks
                                    const MAX_CONVERSATION_LOG_SIZE = 500; // Limit conversation log entries
                                    
                                    connectionData.conversationLog.push({
                                        role: 'assistant',
                                        content: text,
                                        timestamp: Date.now(),
                                        messageId: `ai_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
                                    });
                                    
                                    // Remove oldest entries if limit exceeded
                                    if (connectionData.conversationLog.length > MAX_CONVERSATION_LOG_SIZE) {
                                        const removed = connectionData.conversationLog.splice(0, connectionData.conversationLog.length - MAX_CONVERSATION_LOG_SIZE);
                                        console.log(`🧹 [${callSid}] Trimmed ${removed.length} old conversation entries`);
                                    }
                                }

                                // Handle summary collection if requested
                                if (connectionData.summaryRequested) {
                                    connectionData.summaryText += text;
                                }
                            }

                        } catch (error) {
                            console.error(`❌ [${callSid}] Error processing Gemini message:`, error);
                        }
                    }
                },
                config: {
                    responseModalities: [Modality.AUDIO],
                    speechConfig: {
                        voiceConfig: {
                            prebuiltVoiceConfig: {
                                voiceName: config.voice
                            }
                        }
                    }
                },
                temperature: 1.1,
                topP: 0.95,
                topK: 40,
                maxOutputTokens: 8192
            });

            // CRITICAL: Send AI instructions immediately after session creation
            if (config.aiInstructions) {
                console.log(`📝 [${callSid}] Sending AI instructions to Gemini (${config.aiInstructions.length} chars)`);
                console.log(`📝 [${callSid}] AI instructions preview: ${config.aiInstructions.substring(0, 200)}...`);

                try {
                    const instructionsPayload = {
                        turns: [{
                            role: 'user',
                            parts: [{
                                text: config.aiInstructions
                            }]
                        }],
                        turnComplete: true
                    };

                    // Use the session directly
                    await geminiSession.sendClientContent(instructionsPayload);
                    console.log(`✅ [${callSid}] AI instructions sent successfully`);
                } catch (instructionError) {
                    console.error(`❌ [${callSid}] Error sending AI instructions:`, instructionError);
                    console.error(`🔍 [${callSid}] Session available: ${!!connectionData.geminiSession}`);
                }
            } else {
                console.warn(`⚠️ [${callSid}] No AI instructions to send - this may cause issues`);
            }

            // Store the session in connectionData immediately after creation
            // This prevents race conditions where callbacks might execute before assignment
            connectionData.geminiSession = geminiSession;

            return geminiSession;

        } catch (error) {
            console.error(`❌ [${callSid}] Error creating Gemini session:`, error);
            return null;
        }
    }

    // Note: Message handling is now done directly in the onmessage callback

    // Send initial message to AI
    async sendInitialMessage(geminiSession, aiInstructions) {
        try {
            if (geminiSession && aiInstructions) {
                await geminiSession.sendClientContent({
                    turns: [{
                        role: 'user',
                        parts: [{
                            text: aiInstructions
                        }]
                    }],
                    turnComplete: true
                });
                console.log('📤 Initial AI instructions sent');
            }
        } catch (error) {
            console.error('❌ Error sending initial message:', error);
        }
    }

    // Send text to Gemini session
    async sendTextToGemini(callSid, geminiSession, text) {
        if (!callSid || !geminiSession || !text) {
            console.warn(`⚠️ [${callSid}] Missing required parameters for sendTextToGemini`);
            return;
        }

        try {
            console.log(`🔍 [${callSid}] Sending text to Gemini: "${text}"`);
            await geminiSession.sendClientContent({
                turns: [{
                    role: 'user',
                    parts: [{
                        text: text
                    }]
                }],
                turnComplete: false
            });
            console.log(`✅ [${callSid}] Text sent to Gemini successfully`);
        } catch (error) {
            console.error(`❌ [${callSid}] Error sending text to Gemini:`, error);
        }
    }

    // Send turn complete signal to Gemini (user finished speaking)
    async sendTurnComplete(callSid, geminiSession) {
        try {
            console.log(`🔍 [${callSid}] Sending turn complete signal to Gemini...`);
            await geminiSession.sendClientContent({
                turns: [{
                    role: 'user',
                    parts: [{
                        text: '[Turn complete - user finished speaking]'
                    }]
                }],
                turnComplete: true
            });
            console.log(`✅ [${callSid}] Turn complete signal sent successfully`);
        } catch (error) {
            console.error(`❌ [${callSid}] Error sending turn complete:`, error);
        }
    }

    // Send audio to Gemini session (for Twilio calls with μ-law audio)
    async sendAudioToGemini(callSid, geminiSession, audioBuffer) {
        try {
            console.log(`🔍 [${callSid}] sendAudioToGemini called - geminiSession: ${!!geminiSession}, audioBuffer: ${!!audioBuffer}, audioSize: ${audioBuffer?.length || 0}`);

            if (!geminiSession || !audioBuffer) {
                console.log(`⚠️ [${callSid}] sendAudioToGemini early return - missing geminiSession or audioBuffer`);
                return;
            }

            console.log(`🔍 [${callSid}] Updating metrics...`);
            // Update metrics with null check
            const metrics = this.sessionMetrics.get(callSid);
            if (metrics) {
                metrics.messagesSent = (metrics.messagesSent || 0) + 1;
                metrics.lastActivity = Date.now();
            }

            console.log(`🔍 [${callSid}] Converting audio format - audioProcessor exists: ${!!this.audioProcessor}`);
            // Convert Twilio audio to Gemini format
            const pcmBuffer = this.audioProcessor.convertUlawToPCM(audioBuffer);
            console.log(`🔍 [${callSid}] PCM conversion complete - buffer size: ${pcmBuffer.length}`);

            const float32Data = this.audioProcessor.pcmToFloat32Array(pcmBuffer);
            console.log(`🔍 [${callSid}] Float32 conversion complete - array length: ${float32Data.length}`);

            const audioBlob = this.audioProcessor.createGeminiAudioBlob(float32Data);
            console.log(`🔍 [${callSid}] Audio blob created - size: ${audioBlob.data?.length || 'N/A'}`);

            console.log(`🔍 [${callSid}] Sending audio to Gemini session...`);
            // Send to Gemini using sendRealtimeInput
            await geminiSession.sendRealtimeInput({
                media: {
                    mimeType: audioBlob.mimeType,
                    data: audioBlob.data
                }
            });
            console.log(`✅ [${callSid}] Audio sent to Gemini successfully`);

        } catch (error) {
            console.error(`❌ [${callSid}] Error sending audio to Gemini:`, error);
        }
    }

    // Send browser PCM audio to Gemini session (for local testing)
    async sendBrowserAudioToGemini(callSid, geminiSession, base64Audio) {
        try {
            console.log(`🔍 [${callSid}] sendBrowserAudioToGemini called - geminiSession: ${!!geminiSession}, audioSize: ${base64Audio?.length || 0}`);

            if (!geminiSession || !base64Audio) {
                console.log(`⚠️ [${callSid}] sendBrowserAudioToGemini early return - missing geminiSession or audio`);
                return;
            }

            // Update metrics with null check
            const metrics = this.sessionMetrics.get(callSid);
            if (metrics) {
                metrics.messagesSent = (metrics.messagesSent || 0) + 1;
                metrics.lastActivity = Date.now();
            }

            // Send audio exactly as received
            console.log(`🎵 [${callSid}] Sending browser audio to Gemini...`);
            
            try {
                // Send audio - just pass the media object
                await geminiSession.sendRealtimeInput({
                    media: {
                        data: base64Audio,
                        mimeType: 'audio/pcm;rate=16000'  // Browser sends PCM16
                    }
                });
                console.log(`✅ [${callSid}] Browser audio sent to Gemini successfully`);
                
                // Add activity timestamp to track continuous flow
                const currentTime = Date.now();
                console.log(`⏱️ [${callSid}] Audio sent at: ${currentTime}, session active: ${!!geminiSession}`);
                
            } catch (sendError) {
                console.error(`🚨 [${callSid}] GEMINI AUDIO SEND ERROR 🚨`);
                console.error(`❌ [${callSid}] Error sending browser audio directly:`, sendError);
                console.error(`❌ [${callSid}] Error message: ${sendError?.message || 'No message'}`);
                
                // Check for quota errors
                if (sendError?.message?.includes('quota') || sendError?.message?.includes('exceeded')) {
                    console.error(`🚨🚨🚨 [${callSid}] QUOTA EXCEEDED IN AUDIO SEND 🚨🚨🚨`);
                    console.error(`💳 [${callSid}] Check your Gemini API billing and quota limits!`);
                }
                
                // Try with WebM mime type as fallback
                try {
                    await geminiSession.sendRealtimeInput({
                        media: {
                            data: base64Audio,
                            mimeType: 'audio/webm'
                        }
                    });
                    console.log(`⚠️ [${callSid}] Browser audio sent with WebM mime type (fallback)`);
                } catch (fallbackError) {
                    console.error(`🚨 [${callSid}] FALLBACK AUDIO SEND ALSO FAILED 🚨`);
                    console.error(`❌ [${callSid}] All audio sending methods failed:`, fallbackError);
                    console.error(`❌ [${callSid}] Fallback error message: ${fallbackError?.message || 'No message'}`);
                    
                    if (fallbackError?.message?.includes('quota') || fallbackError?.message?.includes('exceeded')) {
                        console.error(`🚨🚨🚨 [${callSid}] QUOTA EXCEEDED IN FALLBACK TOO 🚨🚨🚨`);
                    }
                }
            }

        } catch (error) {
            console.error(`❌ [${callSid}] Error sending browser audio to Gemini:`, error);
        }
    }

    // Recover session after interruption
    async recoverSession(callSid, reason) {
        // Atomic check and set to prevent concurrent recovery attempts
        const wasAlreadyRecovering = this.recoveryInProgress.has(callSid);
        if (wasAlreadyRecovering) {
            console.log(`⏳ [${callSid}] Recovery already in progress`);
            return;
        }
        
        // Immediately add to recovery set before any async operations
        this.recoveryInProgress.add(callSid);
        
        try {
            const context = this.contextManager.getSessionContext(callSid);
            if (!context || !this.contextManager.canRecover(callSid)) {
                console.log(`❌ [${callSid}] Cannot recover session`);
                return;
            }

            // Get connection data from active connections (would need to be passed in)
            // This is a simplified version - in practice you'd need access to activeConnections
            const recoveryCount = this.contextManager.incrementRecoveryAttempt(callSid);
            console.log(`🔄 [${callSid}] Attempting session recovery #${recoveryCount} (reason: ${reason})`);

            // Update metrics with null check
            const metrics = this.sessionMetrics.get(callSid);
            if (metrics) {
                metrics.recoveryCount = (metrics.recoveryCount || 0) + 1;
                metrics.lastRecoveryTime = Date.now();
            }

            // The actual recovery would happen in the main connection handler
            // This method primarily handles the recovery logic and context preparation
            
            console.log(`✅ [${callSid}] Recovery preparation completed`);

        } catch (error) {
            console.error(`❌ [${callSid}] Error during session recovery:`, error);
        } finally {
            this.recoveryInProgress.delete(callSid);
        }
    }

    // Generate session summary
    async generateSummary(callSid, connectionData, summaryPrompt) {
        try {
            console.log(`📋 [${callSid}] Generating call summary`);
            
            if (!connectionData?.geminiSession) {
                console.warn(`⚠️ [${callSid}] No Gemini session for summary generation`);
                return;
            }

            connectionData.summaryRequested = true;
            connectionData.summaryText = '';

            // Send summary request
            await connectionData.geminiSession.sendClientContent({
                turns: [{
                    role: 'user',
                    parts: [{
                        text: summaryPrompt
                    }]
                }],
                turnComplete: true
            });

            // Summary will be collected in the onmessage callback
            return true;

        } catch (error) {
            console.error(`❌ [${callSid}] Error generating summary:`, error);
            return false;
        }
    }

    // Get session metrics
    getSessionMetrics(callSid) {
        return this.sessionMetrics.get(callSid) || null;
    }

    // Clean up session
    cleanupSession(callSid) {
        this.sessionMetrics.delete(callSid);
        this.recoveryInProgress.delete(callSid);
        console.log(`🧹 [${callSid}] Session manager cleanup completed`);
    }
}
